<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Polymorphism - Portfolio showcasing the identity of an Ultimate Polymorphism, a visionary who balances engineering, art, and design. Built with Flutter.">
  <meta name="keywords" content="portfolio, flutter, developer, engineer, design, polymorphism, web development">
  <meta name="author" content="<PERSON><PERSON>">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="Polymorphism - Visual Engineer Portfolio">
  <meta property="og:description" content="Portfolio showcasing the identity of an Ultimate Polymorphism, a visionary who balances engineering, art, and design.">
  <meta property="og:image" content="icons/Icon-512.png">
  
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="Polymorphism - Visual Engineer Portfolio">
  <meta property="twitter:description" content="Portfolio showcasing the identity of an Ultimate Polymorphism, a visionary who balances engineering, art, and design.">
  <meta property="twitter:image" content="icons/Icon-512.png">

  <!-- Theme colors -->
  <meta name="theme-color" content="#64FFDA">
  <meta name="msapplication-TileColor" content="#1A1A1A">
  <meta name="msapplication-config" content="browserconfig.xml">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="Polymorphism">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="192x192" href="icons/Icon-192.png">
  <link rel="apple-touch-icon" sizes="512x512" href="icons/Icon-512.png">

  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="favicon.svg"/>
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="shortcut icon" href="favicon.svg">

  <title>Polymorphism - Visual Engineer</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
