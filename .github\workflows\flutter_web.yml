name: Build and Deploy Flutter Web to Vercel

on:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: subosito/flutter-action@v2
        with:
          channel: stable

      - name: Install dependencies
        run: flutter pub get

      - name: Build Flutter Web
        run: |
          flutter build web \
            --dart-define=EMAILJS_SERVICE_ID=${{ secrets.EMAILJS_SERVICE_ID }} \
            --dart-define=EMAILJS_TEMPLATE_ID=${{ secrets.EMAILJS_TEMPLATE_ID }} \
            --dart-define=EMAILJS_PUBLIC_KEY=${{ secrets.EMAILJS_PUBLIC_KEY }}

      - name: Upload build output
        uses: actions/upload-artifact@v3
        with:
          name: web-build
          path: build/web

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Download build output
        uses: actions/download-artifact@v3
        with:
          name: web-build
          path: build/web

      - name: Deploy to Vercel
        uses: vercel/vercel-action@v2
        with:
          working-directory: build/web
          token: ${{ secrets.VERCEL_TOKEN }}
          prod: true